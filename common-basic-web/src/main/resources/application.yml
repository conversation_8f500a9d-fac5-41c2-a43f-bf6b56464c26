app:
  id: common-basic

apollo:
  bootstrap:
    enabled: true
    namespaces: application,config

spring:
  profiles:
    active: local
  application:
    name: common-basic

server:
  port: 8080
  connection-timeout: 30000
  server-header: unknown
  error:
    include-stacktrace: never
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 2048
  http2:
    enabled: true
  tomcat:
    protocol: org.apache.coyote.http11.Http11Nio2Protocol
    max-connections: 10000
    max-threads: 500
    min-spare-threads: 50
    accept-count: 200
    keep-alive-timeout: 30000
    max-keep-alive-requests: 1000
    uri-encoding: UTF-8

logging:
  config: classpath:log4j2-${spring.profiles.active}.xml
