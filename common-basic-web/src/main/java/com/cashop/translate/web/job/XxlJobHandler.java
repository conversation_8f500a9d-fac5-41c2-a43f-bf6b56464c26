package com.cashop.translate.web.job;

import com.cashop.translate.service.job.AsyncTextBatchCallbackJobService;
import com.cashop.translate.service.job.AsyncTextBatchTranslateJobService;
import com.cashop.translate.service.job.BatchTranslateJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * XXL-Job任务处理器
 * 
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "translate.image.batch.result.type", havingValue = "xxl-job")
public class XxlJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(XxlJobHandler.class);

    @Autowired
    private BatchTranslateJobService batchTranslateJobService;
    @Autowired
    private AsyncTextBatchCallbackJobService asyncTextBatchCallbackJobService;
    @Autowired
    private AsyncTextBatchTranslateJobService asyncTextBatchTranslateJobService;

    /**
     * 批量翻译结果获取任务
     */
    @XxlJob("batchTranslateResultJob")
    public void batchTranslateResultJob() {
        logger.info("XXL-Job执行批量翻译结果获取任务");
        try {
            batchTranslateJobService.batchTranslateResultJob();
        } catch (Exception e) {
            logger.error("XXL-Job批量翻译结果获取任务执行异常", e);
            throw e;
        }
    }

    /**
     * 清理超时任务
     */
    @XxlJob("cleanTimeoutTasksJob")
    public void cleanTimeoutTasksJob() {
        logger.info("XXL-Job执行清理超时任务");
        try {
            batchTranslateJobService.cleanTimeoutTasksJob();
        } catch (Exception e) {
            logger.error("XXL-Job清理超时任务执行异常", e);
            throw e;
        }
    }


    /**
     * 处理异步批量文本翻译任务
     */
    @XxlJob("asyncTextBatchTranslateJobHandler")
    public void asyncTextBatchTranslateJobHandler() {
        XxlJobHelper.log("开始执行异步批量文本翻译任务");
        logger.info("开始执行异步批量文本翻译任务");

        try {
            asyncTextBatchTranslateJobService.processAsyncTextBatchTranslateTasks();
            
            XxlJobHelper.log("异步批量文本翻译任务执行完成");
            logger.info("异步批量文本翻译任务执行完成");
            
        } catch (Exception e) {
            String errorMsg = "异步批量文本翻译任务执行异常: " + e.getMessage();
            XxlJobHelper.log(errorMsg);
            logger.error(errorMsg, e);
            XxlJobHelper.handleFail(errorMsg);
        }
    }

    /**
     * 处理异步批量文本翻译回调任务
     */
    @XxlJob("asyncTextBatchCallbackJobHandler")
    public void asyncTextBatchCallbackJobHandler() {
        XxlJobHelper.log("开始执行异步批量文本翻译回调任务");
        logger.info("开始执行异步批量文本翻译回调任务");

        try {
            asyncTextBatchCallbackJobService.processAsyncTextBatchCallbackTasks();
            
            XxlJobHelper.log("异步批量文本翻译回调任务执行完成");
            logger.info("异步批量文本翻译回调任务执行完成");
            
        } catch (Exception e) {
            String errorMsg = "异步批量文本翻译回调任务执行异常: " + e.getMessage();
            XxlJobHelper.log(errorMsg);
            logger.error(errorMsg, e);
            XxlJobHelper.handleFail(errorMsg);
        }
    }
    

}
