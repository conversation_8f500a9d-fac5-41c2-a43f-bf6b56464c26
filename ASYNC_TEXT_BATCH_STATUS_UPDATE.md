# 异步批量文本翻译状态字段调整

## 需求描述

调整异步批量文本翻译任务(AsyncTextBatchTranslateJobService.processAsyncTextBatchTranslateTasks)中的状态更新逻辑：
- **修改前**: 更新 `cloudApiStatus` 和 `cloudApiResponse` 字段
- **修改后**: 更新 `cloudApiAsyncStatus` 和 `cloudApiAsyncResponse` 字段

## 修改内容

### 1. 新增 Mapper 方法

在 `TranslateRequestRecordMapper.java` 中新增方法：

```java
/**
 * 更新异步批量文本翻译状态和响应
 */
int updateAsyncTextBatchStatus(@Param("id") Long id,
                              @Param("cloudApiAsyncStatus") String cloudApiAsyncStatus,
                              @Param("cloudApiAsyncResponse") String cloudApiAsyncResponse,
                              @Param("requestStatus") String requestStatus,
                              @Param("retryCount") Integer retryCount,
                              @Param("errorMessage") String errorMessage,
                              @Param("textBatchResults") String textBatchResults);
```

### 2. 新增 SQL 映射

在 `TranslateRequestRecordMapper.xml` 中新增 SQL：

```xml
<update id="updateAsyncTextBatchStatus">
    UPDATE translate_request_record
    SET cloud_api_async_status = #{cloudApiAsyncStatus},
        cloud_api_async_response = #{cloudApiAsyncResponse},
        request_status = #{requestStatus},
        retry_count = #{retryCount},
        error_message = #{errorMessage},
        text_batch_results = #{textBatchResults}
    WHERE id = #{id}
</update>
```

### 3. 修改服务类

在 `AsyncTextBatchTranslateJobService.java` 中的三个位置进行修改：

#### 3.1 异常处理时的状态更新
```java
// 修改前
translateRequestRecordMapper.updateCloudApiStatus(...)

// 修改后  
translateRequestRecordMapper.updateAsyncTextBatchStatus(...)
```

#### 3.2 开始处理时的状态更新
```java
// 修改前
translateRequestRecordMapper.updateCloudApiStatus(...)

// 修改后
translateRequestRecordMapper.updateAsyncTextBatchStatus(...)
```

#### 3.3 任务结果更新
```java
// 修改前
translateRequestRecordMapper.updateCloudApiStatus(...)

// 修改后
translateRequestRecordMapper.updateAsyncTextBatchStatus(...)
```

## 字段对比

| 场景 | 修改前字段 | 修改后字段 | 说明 |
|------|------------|------------|------|
| 异步批量文本翻译 | cloudApiStatus | cloudApiAsyncStatus | 异步状态字段 |
| 异步批量文本翻译 | cloudApiResponse | cloudApiAsyncResponse | 异步响应字段 |
| 翻译结果存储 | 通过updateCloudApiStatus更新text_batch_results | 通过updateAsyncTextBatchStatus更新text_batch_results | 专用方法更新 |

## 影响范围

### 修改的文件
1. `common-basic-dao/src/main/java/com/cashop/translate/dao/mapper/TranslateRequestRecordMapper.java`
2. `common-basic-dao/src/main/resources/mapper/TranslateRequestRecordMapper.xml`
3. `common-basic-service/src/main/java/com/cashop/translate/service/job/AsyncTextBatchTranslateJobService.java`

### 不受影响的功能
- 同步翻译功能
- 异步批量图片翻译功能（继续使用原有的updateAsyncStatus方法）
- 其他业务逻辑

## 设计原理

### 状态字段的职责分工
- **cloudApiStatus/cloudApiResponse**: Controller请求转到云服务供应商接口时的状态和响应
- **cloudApiAsyncStatus/cloudApiAsyncResponse**: 批量翻译定时任务中请求云服务供应商接口的状态和响应

### 为什么需要专门的方法
1. **字段区分**: 异步批量文本翻译需要更新`text_batch_results`字段，而不是`image_batch_results`
2. **语义清晰**: 使用专门的方法名`updateAsyncTextBatchStatus`更清楚地表达意图
3. **维护性**: 避免混用方法，降低出错概率

## 验证方法

1. **检查数据库更新**: 确认异步批量文本翻译任务执行后，`cloud_api_async_status`和`cloud_api_async_response`字段被正确更新
2. **检查结果存储**: 确认翻译结果正确存储在`text_batch_results`字段中
3. **功能测试**: 验证异步批量文本翻译的完整流程正常工作

## 总结

✅ **修改完成**

通过新增专门的`updateAsyncTextBatchStatus`方法，异步批量文本翻译任务现在正确地更新`cloudApiAsyncStatus`和`cloudApiAsyncResponse`字段，符合设计规范和业务逻辑。
