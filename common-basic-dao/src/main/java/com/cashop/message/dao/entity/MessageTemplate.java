package com.cashop.message.dao.entity;

import java.util.Date;

/**
 * 消息模版实体
 * 
 * <AUTHOR>
 */
public class MessageTemplate {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 语言
     */
    private String language;

    /**
     * 消息类型：email/sms/push
     */
    private String messageType;

    /**
     * 模版标题
     */
    private String messageTitle;

    /**
     * 模版内容
     */
    private String messageContent;

    /**
     * 备注
     */
    private String messageRemark;

    /**
     * 重试开关：0-关闭，1-开启
     */
    private Boolean retryConfig;

    /**
     * 消息供应商：qq_email/google_email等
     */
    private String providerKey;

    /**
     * 消息接收人列表
     */
    private String recipients;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getMessageRemark() {
        return messageRemark;
    }

    public void setMessageRemark(String messageRemark) {
        this.messageRemark = messageRemark;
    }

    public Boolean getRetryConfig() {
        return retryConfig;
    }

    public void setRetryConfig(Boolean retryConfig) {
        this.retryConfig = retryConfig;
    }

    public String getProviderKey() {
        return providerKey;
    }

    public void setProviderKey(String providerKey) {
        this.providerKey = providerKey;
    }

    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        return "MessageTemplate{" +
                "id=" + id +
                ", sceneCode='" + sceneCode + '\'' +
                ", language='" + language + '\'' +
                ", messageType='" + messageType + '\'' +
                ", messageTitle='" + messageTitle + '\'' +
                ", messageContent='" + messageContent + '\'' +
                ", messageRemark='" + messageRemark + '\'' +
                ", retryConfig=" + retryConfig +
                ", providerKey='" + providerKey + '\'' +
                ", recipients='" + recipients + '\'' +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                '}';
    }
}
