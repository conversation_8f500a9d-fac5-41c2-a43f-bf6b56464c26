package com.cashop.message.dao.entity;

import java.util.Date;

/**
 * 邮件发送记录实体
 * 
 * <AUTHOR>
 */
public class MessageEmailRequest {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 语言
     */
    private String language;

    /**
     * 邮件服务端host
     */
    private String emailHost;

    /**
     * 邮件服务端port
     */
    private Integer emailPort;

    /**
     * 发件人账号
     */
    private String fromEmail;

    /**
     * 发件人密码
     */
    private String fromEmailPwd;

    /**
     * 接收人列表JSON
     */
    private String toEmailList;

    /**
     * 抄送人列表JSON
     */
    private String ccEmailList;

    /**
     * 附件列表JSON
     */
    private String attachmentList;

    /**
     * 请求标题
     */
    private String requestTitle;

    /**
     * 模版参数JSON
     */
    private String templateParams;

    /**
     * 实际发送标题
     */
    private String actualTitle;

    /**
     * 实际发送内容
     */
    private String actualContent;

    /**
     * 当前执行次数
     */
    private Integer currentRetryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 发送状态：PENDING/PROCESSING/SUCCESS/FAILED
     */
    private String sendStatus;

    /**
     * 发送异常信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getEmailHost() {
        return emailHost;
    }

    public void setEmailHost(String emailHost) {
        this.emailHost = emailHost;
    }

    public Integer getEmailPort() {
        return emailPort;
    }

    public void setEmailPort(Integer emailPort) {
        this.emailPort = emailPort;
    }

    public String getFromEmail() {
        return fromEmail;
    }

    public void setFromEmail(String fromEmail) {
        this.fromEmail = fromEmail;
    }

    public String getFromEmailPwd() {
        return fromEmailPwd;
    }

    public void setFromEmailPwd(String fromEmailPwd) {
        this.fromEmailPwd = fromEmailPwd;
    }

    public String getToEmailList() {
        return toEmailList;
    }

    public void setToEmailList(String toEmailList) {
        this.toEmailList = toEmailList;
    }

    public String getCcEmailList() {
        return ccEmailList;
    }

    public void setCcEmailList(String ccEmailList) {
        this.ccEmailList = ccEmailList;
    }

    public String getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(String attachmentList) {
        this.attachmentList = attachmentList;
    }

    public String getRequestTitle() {
        return requestTitle;
    }

    public void setRequestTitle(String requestTitle) {
        this.requestTitle = requestTitle;
    }

    public String getTemplateParams() {
        return templateParams;
    }

    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams;
    }

    public String getActualTitle() {
        return actualTitle;
    }

    public void setActualTitle(String actualTitle) {
        this.actualTitle = actualTitle;
    }

    public String getActualContent() {
        return actualContent;
    }

    public void setActualContent(String actualContent) {
        this.actualContent = actualContent;
    }

    public Integer getCurrentRetryCount() {
        return currentRetryCount;
    }

    public void setCurrentRetryCount(Integer currentRetryCount) {
        this.currentRetryCount = currentRetryCount;
    }

    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }

    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }

    public String getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }
}
