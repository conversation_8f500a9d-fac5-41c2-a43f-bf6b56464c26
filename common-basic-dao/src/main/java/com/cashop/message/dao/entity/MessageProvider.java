package com.cashop.message.dao.entity;

import java.util.Date;

/**
 * 消息供应商实体
 * 
 * <AUTHOR>
 */
public class MessageProvider {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模版类型：email/sms/push
     */
    private String messageType;

    /**
     * 供应商名称
     */
    private String providerKey;

    /**
     * 用户名
     */
    private String providerUserName;

    /**
     * 登录信息
     */
    private String providerUserPwd;

    /**
     * 供应商配置信息JSON
     */
    private String providerConfig;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getProviderKey() {
        return providerKey;
    }

    public void setProviderKey(String providerKey) {
        this.providerKey = providerKey;
    }

    public String getProviderUserName() {
        return providerUserName;
    }

    public void setProviderUserName(String providerUserName) {
        this.providerUserName = providerUserName;
    }

    public String getProviderUserPwd() {
        return providerUserPwd;
    }

    public void setProviderUserPwd(String providerUserPwd) {
        this.providerUserPwd = providerUserPwd;
    }

    public String getProviderConfig() {
        return providerConfig;
    }

    public void setProviderConfig(String providerConfig) {
        this.providerConfig = providerConfig;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        return "MessageProvider{" +
                "id=" + id +
                ", messageType='" + messageType + '\'' +
                ", providerKey='" + providerKey + '\'' +
                ", providerUserName='" + providerUserName + '\'' +
                ", providerUserPwd='" + providerUserPwd + '\'' +
                ", providerConfig='" + providerConfig + '\'' +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                '}';
    }
}
