package com.cashop.translate.client.aliyun;

import com.aliyun.sdk.service.alimt20181012.AsyncClient;
import com.aliyun.sdk.service.alimt20181012.models.*;
import com.aliyun.sdk.service.alimt20181012.models.GetTranslateImageBatchResultResponseBody.Result;
import com.cashop.translate.common.dto.TranslateImageBatchResult;
import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.dto.TranslateTextBatchResult;
import com.cashop.translate.common.enums.RequestStatusEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 阿里云翻译客户端
 * 
 * <AUTHOR>
 */
@Component
public class AliyunTranslateClient {

    private static final Logger logger = LoggerFactory.getLogger(AliyunTranslateClient.class);

    @Autowired
    private AsyncClient aliyunTranslateAsyncClient;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 同步图片翻译
     */
    public CompletableFuture<TranslateResponse> translateImageSync(TranslateRequest request) {
        logger.info("开始调用阿里云同步图片翻译，requestId: {}", request.getRequestId());
        
        try {
            // 构建请求参数
            TranslateImageRequest.Builder requestBuilder = TranslateImageRequest.builder()
                    .targetLanguage(request.getTargetLanguage());

            // 设置源语言（可选）
            if (StringUtils.hasText(request.getSourceLanguage())) {
                requestBuilder.sourceLanguage(request.getSourceLanguage());
            }

            // 设置图片URL或Base64
            if (!CollectionUtils.isEmpty(request.getImageUrls())) {
                requestBuilder.imageUrl(request.getImageUrls().get(0));
            } else if (!CollectionUtils.isEmpty(request.getImageBase64List())) {
                requestBuilder.imageBase64(request.getImageBase64List().get(0));
            } else {
                throw new IllegalArgumentException("图片URL或Base64不能为空");
            }

            // 设置图片翻译场景（Field参数），如果未指定则使用默认值
            String field = StringUtils.hasText(request.getImageScene()) ? request.getImageScene() : "general";
            requestBuilder.field(field);

            // 设置扩展参数
            if (StringUtils.hasText(request.getExt())) {
                requestBuilder.ext(request.getExt());
            }

            TranslateImageRequest translateRequest = requestBuilder.build();

            // 调用阿里云API
            return aliyunTranslateAsyncClient.translateImage(translateRequest)
                    .thenApply(response -> {
                        if(200 != response.getBody().getCode()) {
                            TranslateResponse result = new TranslateResponse();
                            result.setRequestId(request.getRequestId());
                            result.setSuccess(false);
                            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                            result.setErrorMessage("阿里云同步图片翻译调用失败: " + response.getBody().getCode() + "-" + response.getBody().getMessage());
                            result.setResponseCode(response.getBody().getCode());
                            return result;
                        }
                        TranslateResponse result = new TranslateResponse();
                        result.setRequestId(request.getRequestId());
                        result.setSuccess(true);
                        result.setCloudApiStatus(RequestStatusEnum.SUCCESS.getCode());
                        result.setRequestStatus(RequestStatusEnum.SUCCESS.getCode());
                        result.setResponseCode(response.getBody().getCode());
                        try {
                            // 将响应转换为JSON字符串
                            String responseJson = objectMapper.writeValueAsString(response.getBody());
                            result.setCloudApiResponse(responseJson);
                            // 提取同步翻译结果URL
                            if (response.getBody() != null && response.getBody().getData() != null) {
                                result.setTranslateImageResult(response.getBody().getData().getFinalImageUrl());
                            }
                            logger.info("阿里云同步图片翻译调用成功，requestId: {}", request.getRequestId());
                            
                        } catch (Exception e) {
                            logger.error("阿里云同步图片翻译响应序列化失败，requestId: {}", request.getRequestId(), e);
                            result.setErrorMessage("响应序列化失败: " + e.getMessage());
                        }
                        
                        return result;
                    })
                    .exceptionally(throwable -> {
                        logger.error("阿里云同步图片翻译调用失败，requestId: {}", request.getRequestId(), throwable);
                        
                        TranslateResponse result = new TranslateResponse();
                        result.setRequestId(request.getRequestId());
                        result.setSuccess(false);
                        result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                        result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                        result.setErrorMessage("阿里云同步图片翻译调用失败: " + throwable.getMessage());
                        result.setResponseCode(500);
                        return result;
                    });
                    
        } catch (Exception e) {
            logger.error("阿里云同步图片翻译请求构建失败，requestId: {}", request.getRequestId(), e);
            TranslateResponse result = new TranslateResponse();
            result.setRequestId(request.getRequestId());
            result.setSuccess(false);
            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
            result.setErrorMessage("请求构建失败: " + e.getMessage());
            result.setResponseCode(500);
            return CompletableFuture.completedFuture(result);
        }
    }

    /**
     * 批量图片翻译
     */
    public CompletableFuture<TranslateResponse> translateImageBatch(TranslateRequest request) {
        logger.info("开始调用阿里云批量图片翻译，requestId: {}", request.getRequestId());
        
        try {
            // 构建请求参数
            TranslateImageBatchRequest.Builder requestBuilder = TranslateImageBatchRequest.builder()
                    .targetLanguage(request.getTargetLanguage())
                    .customTaskId(request.getRequestId()); // 使用requestId作为CustomTaskId

            // 设置源语言（可选）
            if (StringUtils.hasText(request.getSourceLanguage())) {
                requestBuilder.sourceLanguage(request.getSourceLanguage());
            }

            // 设置图片URL列表
            if (!CollectionUtils.isEmpty(request.getImageUrls())) {
                // 将List<String>转换为逗号分隔的字符串
                String imageUrlsStr = String.join(",", request.getImageUrls());
                requestBuilder.imageUrls(imageUrlsStr);
            } else {
                throw new IllegalArgumentException("批量翻译图片URL列表不能为空");
            }

            // 设置图片翻译场景（Field参数），如果未指定则使用默认值
            String field = StringUtils.hasText(request.getImageScene()) ? request.getImageScene() : "general";
            requestBuilder.field(field);

            // 设置扩展参数
            if (StringUtils.hasText(request.getExt())) {
                requestBuilder.ext(request.getExt());
            }

            TranslateImageBatchRequest batchRequest = requestBuilder.build();

            // 调用阿里云API
            return aliyunTranslateAsyncClient.translateImageBatch(batchRequest)
                    .thenApply(response -> {

                        if(200 != response.getBody().getCode()) {
                            TranslateResponse result = new TranslateResponse();
                            result.setRequestId(request.getRequestId());
                            result.setSuccess(false);
                            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                            result.setErrorMessage("阿里云批量图片翻译调用失败: " + response.getBody().getCode() + "-" + response.getBody().getMessage());
                            result.setResponseCode(response.getBody().getCode());
                            return result;
                        }

                        TranslateResponse result = new TranslateResponse();
                        result.setRequestId(request.getRequestId());
                        result.setSuccess(true);
                        result.setCloudApiStatus(RequestStatusEnum.SUCCESS.getCode());
                        result.setRequestStatus(RequestStatusEnum.PROCESSING.getCode());//等待查询异步结果job调度
                        result.setResponseCode(response.getBody().getCode());
                        try {
                            // 将响应转换为JSON字符串
                            String responseJson = objectMapper.writeValueAsString(response.getBody());
                            result.setCloudApiResponse(responseJson);
                            
                            // 提取taskId
                            if (response.getBody() != null && response.getBody().getData() != null) {
                                result.setTaskId(response.getBody().getData().getTaskId());
                            }
                            
                            logger.info("阿里云批量图片翻译调用成功，requestId: {}, taskId: {}", 
                                    request.getRequestId(), result.getTaskId());
                            
                        } catch (Exception e) {
                            logger.error("阿里云批量图片翻译响应序列化失败，requestId: {}", request.getRequestId(), e);
                            result.setErrorMessage("响应序列化失败: " + e.getMessage());
                        }
                        
                        return result;
                    })
                    .exceptionally(throwable -> {
                        logger.error("阿里云批量图片翻译调用失败，requestId: {}", request.getRequestId(), throwable);
                        
                        TranslateResponse result = new TranslateResponse();
                        result.setRequestId(request.getRequestId());
                        result.setSuccess(false);
                        result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                        result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                        result.setErrorMessage("阿里云批量图片翻译调用失败: " + throwable.getMessage());
                        result.setResponseCode(500);
                        return result;
                    });
                    
        } catch (Exception e) {
            logger.error("阿里云批量图片翻译请求构建失败，requestId: {}", request.getRequestId(), e);
            TranslateResponse result = new TranslateResponse();
            result.setRequestId(request.getRequestId());
            result.setSuccess(false);
            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
            result.setErrorMessage("请求构建失败: " + e.getMessage());
            result.setResponseCode(500);
            return CompletableFuture.completedFuture(result);
        }
    }

    /**
     * 获取批量翻译结果
     */
    public CompletableFuture<TranslateResponse> getBatchTranslateResult(String taskId) {
        logger.info("开始获取阿里云批量翻译结果，taskId: {}", taskId);
        
        try {
            // 构建请求参数
            GetTranslateImageBatchResultRequest resultRequest = GetTranslateImageBatchResultRequest.builder()
                    .taskId(taskId)
                    .build();

            // 调用阿里云API
            return aliyunTranslateAsyncClient.getTranslateImageBatchResult(resultRequest)
                    .thenApply(response -> {

                        if(200 != response.getBody().getCode()) {
                            TranslateResponse result = new TranslateResponse();
                            result.setTaskId(taskId);
                            result.setSuccess(false);
                            result.setCloudApiAsyncStatus(RequestStatusEnum.PROCESSING.getCode());
                            result.setRequestStatus(RequestStatusEnum.PROCESSING.getCode());
                            result.setErrorMessage("阿里云获取批量翻译调用失败: " + response.getBody().getCode() + "-" + response.getBody().getMessage());
                            result.setResponseCode(response.getBody().getCode());
                            return result;
                        }

                        TranslateResponse result = new TranslateResponse();
                        result.setTaskId(taskId);
                        /*
                        标识当前任务的运行状态，可能的值及含义如下：
                        created：批量翻译任务创建成功，等待运行
                        running：批量翻译任务正在运行中
                        finished：批量翻译任务运行结束，图片均已翻译完毕，此时 Result 字段会包含每张图片的翻译结果
                        invalid：传入的任务 ID 非法，或者任务的翻译结果已经过期被清理
                        */
                        if(!"finished".equals(response.getBody().getData().getStatus())) {
                            result.setSuccess(false);
                            result.setCloudApiAsyncStatus(RequestStatusEnum.PROCESSING.getCode());
                            result.setRequestStatus(RequestStatusEnum.PROCESSING.getCode());
                            result.setErrorMessage("运行状态: " + response.getBody().getData().getStatus());
                            result.setResponseCode(500);
                            logger.error("阿里云批量翻译结果API调用异常，taskId: {}", taskId);
                            return result;
                        }
                        result.setSuccess(true);
                        result.setCloudApiAsyncStatus(RequestStatusEnum.SUCCESS.getCode());
                        result.setRequestStatus(RequestStatusEnum.SUCCESS.getCode());
                        result.setResponseCode(response.getBody().getCode());
                        try {
                            // 将响应转换为JSON字符串
                            String responseJson = objectMapper.writeValueAsString(response.getBody());
                            result.setCloudApiAsyncResponse(responseJson);
                            
                            //提取批量翻译结果
                            List<TranslateImageBatchResult> translateImageAsyncResults = new ArrayList<TranslateImageBatchResult>();
                            if (response.getBody() != null && response.getBody().getData() != null) {
                                java.util.List<Result> batchResults = response.getBody().getData().getResult();
                                for (Result batchResult : batchResults) {
                                    TranslateImageBatchResult translateImageAsyncResult = new TranslateImageBatchResult();
                                    translateImageAsyncResult.setCode(batchResult.getCode());
                                    translateImageAsyncResult.setMessage(batchResult.getMessage());
                                    translateImageAsyncResult.setSuccess(batchResult.getSuccess());
                                    translateImageAsyncResult.setSourceImageUrl(batchResult.getSourceImageUrl());
                                    translateImageAsyncResult.setFinalImageUrl(batchResult.getFinalImageUrl());
                                    translateImageAsyncResults.add(translateImageAsyncResult);
                                }
                            }
                            result.setTranslateImageBatchResults(translateImageAsyncResults);
                            logger.info("阿里云批量翻译结果获取成功，taskId: {}", taskId);
                            
                        } catch (Exception e) {
                            logger.error("阿里云批量翻译结果响应解析失败，taskId: {}", taskId, e);
                        }
                        
                        return result;
                    })
                    .exceptionally(throwable -> {
                        TranslateResponse result = new TranslateResponse();
                        result.setTaskId(taskId);
                        result.setSuccess(false);
                        result.setCloudApiAsyncStatus(RequestStatusEnum.FAILED.getCode());
                        result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                        result.setErrorMessage("API调用异常: " + throwable.getMessage());
                        result.setResponseCode(500);
                        logger.error("阿里云批量翻译结果API调用异常，taskId: {}", taskId, throwable);
                        return result;
                    });
                    
        } catch (Exception e) {
            logger.error("阿里云批量翻译结果请求构建失败，taskId: {}", taskId, e);
            TranslateResponse result = new TranslateResponse();
            result.setTaskId(taskId);
            result.setSuccess(false);
            result.setCloudApiAsyncStatus(RequestStatusEnum.FAILED.getCode());
            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
            result.setErrorMessage("请求构建失败: " + e.getMessage());
            result.setResponseCode(500);
            return CompletableFuture.completedFuture(result);
        }
    }

    /**
     * 同步文本翻译
     */
    public CompletableFuture<TranslateResponse> translateTextSync(TranslateRequest request) {
        logger.info("开始调用阿里云同步文本翻译，requestId: {}", request.getRequestId());

        try {
            // 构建请求参数
            com.aliyun.sdk.service.alimt20181012.models.TranslateRequest.Builder requestBuilder =
                    com.aliyun.sdk.service.alimt20181012.models.TranslateRequest.builder()
                    .targetLanguage(request.getTargetLanguage())
                    .sourceText(request.getText());

            // 设置文本格式类型，如果未指定则使用默认值
            String formatType = StringUtils.hasText(request.getFormatType()) ? request.getFormatType() : "text";
            requestBuilder.formatType(formatType);

            // 设置翻译场景，如果未指定则使用默认值
            String scene = StringUtils.hasText(request.getScene()) ? request.getScene() : "general";
            requestBuilder.scene(scene);

            // 设置源语言（可选）
            if (StringUtils.hasText(request.getSourceLanguage())) {
                requestBuilder.sourceLanguage(request.getSourceLanguage());
            } else {
                requestBuilder.sourceLanguage("auto"); // 自动检测
            }

            com.aliyun.sdk.service.alimt20181012.models.TranslateRequest translateRequest = requestBuilder.build();

            // 调用阿里云SDK
            return aliyunTranslateAsyncClient.translate(translateRequest)
                    .thenApply(response -> {
                        logger.info("阿里云文本翻译调用成功，requestId: {}", request.getRequestId());
                        if(200 != response.getBody().getCode()) {
                            TranslateResponse result = new TranslateResponse();
                            result.setRequestId(request.getRequestId());
                            result.setSuccess(false);
                            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                            result.setErrorMessage("阿里云文本翻译调用失败: " + response.getBody().getCode() + "-" + response.getBody().getMessage());
                            result.setResponseCode(response.getBody().getCode());
                            return result;
                        }
                        TranslateResponse result = new TranslateResponse();
                        result.setRequestId(request.getRequestId());
                        result.setSuccess(true);
                        result.setCloudApiStatus(RequestStatusEnum.SUCCESS.getCode());
                        result.setRequestStatus(RequestStatusEnum.SUCCESS.getCode());
                        result.setResponseCode(response.getBody().getCode());

                        try {
                            // 将响应转换为JSON字符串
                            String responseJson = objectMapper.writeValueAsString(response.getBody());
                            result.setCloudApiResponse(responseJson);
                            // 提取文本翻译结果
                            if (response.getBody() != null && response.getBody().getData() != null) {
                                result.setTranslateTextResult(response.getBody().getData().getTranslated());
                            }
                            logger.info("阿里云文本翻译响应: {}", responseJson);

                        } catch (Exception e) {
                            logger.error("阿里云文本翻译响应序列化失败，requestId: {}", request.getRequestId(), e);
                            result.setErrorMessage("响应序列化失败: " + e.getMessage());
                        }

                        return result;
                    })
                    .exceptionally(throwable -> {
                        logger.error("阿里云文本翻译调用失败，requestId: {}", request.getRequestId(), throwable);

                        TranslateResponse result = new TranslateResponse();
                        result.setRequestId(request.getRequestId());
                        result.setSuccess(false);
                        result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                        result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                        result.setErrorMessage("阿里云文本翻译调用失败: " + throwable.getMessage());
                        result.setResponseCode(500);
                        return result;
                    });

        } catch (Exception e) {
            logger.error("阿里云文本翻译请求构建失败，requestId: {}", request.getRequestId(), e);
            TranslateResponse result = new TranslateResponse();
            result.setRequestId(request.getRequestId());
            result.setSuccess(false);
            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
            result.setErrorMessage("请求构建失败: " + e.getMessage());
            result.setResponseCode(500);
            return CompletableFuture.completedFuture(result);
        }
    }

    /**
     * 异步批量文本翻译
     */
    public CompletableFuture<TranslateResponse> translateTextBatch(TranslateRequest request) {
        logger.info("开始调用阿里云异步批量文本翻译，requestId: {}", request.getRequestId());

        try {
            // 阿里云目前不支持批量文本翻译，这里通过循环调用单条文本翻译来实现
            // 实际项目中可以考虑使用其他支持批量文本翻译的服务商
            if (CollectionUtils.isEmpty(request.getTextList())) {
                throw new IllegalArgumentException("批量翻译文本列表不能为空");
            }

            //List<CompletableFuture<TranslateTextBatchResult>> futures = new ArrayList<>();
            TranslateResponse response = new TranslateResponse();
            response.setRequestId(request.getRequestId());
            response.setSuccess(true);
            response.setCloudApiAsyncStatus(RequestStatusEnum.SUCCESS.getCode());
            response.setRequestStatus(RequestStatusEnum.SUCCESS.getCode());
            List<TranslateTextBatchResult> translatedTexts = new ArrayList<>();
            for (String text : request.getTextList()) {
                // 为每个文本创建单独的请求
                TranslateRequest singleRequest = new TranslateRequest();
                singleRequest.setRequestId(request.getRequestId() + "_" + System.currentTimeMillis());
                singleRequest.setSourceLanguage(request.getSourceLanguage());
                singleRequest.setTargetLanguage(request.getTargetLanguage());
                singleRequest.setText(text);
                singleRequest.setFormatType(request.getFormatType());
                singleRequest.setScene(request.getScene());

                CompletableFuture<TranslateTextBatchResult> future = translateTextSync(singleRequest)
                        .thenApply(rsp -> {
                            TranslateTextBatchResult result = new TranslateTextBatchResult();
                            result.setSourceText(text);
                            result.setFinalText(rsp.getTranslateTextResult());
                            result.setSuccess(rsp.isSuccess());
                            result.setMessage(rsp.getErrorMessage());
                            result.setCode(rsp.getResponseCode());
                            return result;
                        }).exceptionally(throwable -> {
                            logger.error("执行文本翻译失败", throwable);
                            TranslateTextBatchResult result = new TranslateTextBatchResult();
                            result.setSourceText(text);
                            result.setFinalText(null);
                            result.setSuccess(false);
                            result.setMessage("文本翻译失败: " + throwable.getMessage());
                            result.setCode(500);
                            return result;
                        });
                //futures.add(future);
                try {
                    translatedTexts.add(future.get());
                } catch (Exception e) {
                    logger.error("获取翻译结果失败", e);
                    translatedTexts.add(new TranslateTextBatchResult(500, "翻译失败", false, text, null));
                }

            }
            try {
                // 将翻译结果列表转换为JSON字符串存储
                String translatedTextsJson = objectMapper.writeValueAsString(translatedTexts);
                response.setTranslateTextResult(translatedTextsJson);
                logger.info("阿里云批量文本翻译完成，requestId: {}, 文本数量: {}",
                        request.getRequestId(), translatedTexts.size());
            } catch (Exception e) {
                logger.error("批量文本翻译结果序列化失败，requestId: {}", request.getRequestId(), e);
                response.setErrorMessage("结果序列化失败: " + e.getMessage());
            }

            return CompletableFuture.completedFuture(response);

            /*
            // 等待所有翻译完成
            return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> {
                        List<TranslateTextBatchResult> translatedTexts = new ArrayList<>();
                        for (CompletableFuture<TranslateTextBatchResult> future : futures) {
                            try {
                                translatedTexts.add(future.get());
                            } catch (Exception e) {
                                logger.error("获取翻译结果失败", e);
                                //translatedTexts.add(new TranslateTextBatchResult("翻译失败", false, text, null));
                            }
                        }

                        TranslateResponse result = new TranslateResponse();
                        result.setRequestId(request.getRequestId());
                        result.setSuccess(true);
                        result.setCloudApiStatus(RequestStatusEnum.SUCCESS.getCode());
                        result.setRequestStatus(RequestStatusEnum.SUCCESS.getCode());

                        try {
                            // 将翻译结果列表转换为JSON字符串存储
                            String translatedTextsJson = objectMapper.writeValueAsString(translatedTexts);
                            result.setTranslateTextResult(translatedTextsJson);
                            logger.info("阿里云批量文本翻译完成，requestId: {}, 文本数量: {}",
                                    request.getRequestId(), translatedTexts.size());
                        } catch (Exception e) {
                            logger.error("批量文本翻译结果序列化失败，requestId: {}", request.getRequestId(), e);
                            result.setErrorMessage("结果序列化失败: " + e.getMessage());
                        }

                        return result;
                    })
                    .exceptionally(throwable -> {
                        logger.error("阿里云批量文本翻译失败，requestId: {}", request.getRequestId(), throwable);
                        TranslateResponse result = new TranslateResponse();
                        result.setRequestId(request.getRequestId());
                        result.setSuccess(false);
                        result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                        result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                        result.setErrorMessage("批量文本翻译失败: " + throwable.getMessage());
                        return result;
                    });
            */
        } catch (Exception e) {
            logger.error("阿里云批量文本翻译请求构建失败，requestId: {}", request.getRequestId(), e);
            TranslateResponse result = new TranslateResponse();
            result.setRequestId(request.getRequestId());
            result.setSuccess(false);
            result.setCloudApiAsyncStatus(RequestStatusEnum.FAILED.getCode());
            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
            result.setErrorMessage("请求构建失败: " + e.getMessage());
            return CompletableFuture.completedFuture(result);
        }
        
    }

    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        try {
            return aliyunTranslateAsyncClient != null;
        } catch (Exception e) {
            logger.error("检查阿里云翻译服务可用性失败", e);
            return false;
        }
    }
}
