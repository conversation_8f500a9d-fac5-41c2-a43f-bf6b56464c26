package com.cashop.translate.service.job;

import com.cashop.translate.service.config.ProviderRouteConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 本地定时任务配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(name = "translate.image.batch.result.type", havingValue = "local-job", matchIfMissing = true)
public class LocalScheduleConfig {

    private static final Logger logger = LoggerFactory.getLogger(LocalScheduleConfig.class);

    @Autowired
    private BatchTranslateJobService batchTranslateJobService;
    @Autowired
    private TranslateCallbackJobService translateCallbackJobService;
    @Autowired
    private AsyncTextBatchTranslateJobService asyncTextBatchTranslateJobService;
    @Autowired
    private AsyncTextBatchCallbackJobService asyncTextBatchCallbackJobService;
    
    @Autowired
    private ProviderRouteConfig routeConfig;
    
    /**
     * 批量翻译结果获取任务
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 1 * 60 * 1000)
    public void batchTranslateResultJob() {
        if (!"local-job".equals(routeConfig.getBatchResultType())) {
            return;
        }
        
        try {
            batchTranslateJobService.batchTranslateResultJob();
        } catch (Exception e) {
            logger.error("本地批量翻译结果获取任务执行异常", e);
        }
    }

    /**
     * 每5分钟执行一次翻译结果回调任务
     */
    @Scheduled(fixedRate = 1 * 60 * 1000) // 5分钟
    public void executeCallbackJob() {
        if (!"local-job".equals(routeConfig.getBatchResultType())) {
            return;
        }
        try {
            logger.debug("开始执行翻译结果回调定时任务");
            translateCallbackJobService.executeCallbackJob();
        } catch (Exception e) {
            logger.error("执行翻译结果回调定时任务失败", e);
        }
    }



    /**
     * 处理异步批量文本翻译任务
     */
    //@XxlJob("asyncTextBatchTranslateJobHandler")
    @Scheduled(fixedRate = 1 * 60 * 1000) // 5分钟
    public void asyncTextBatchTranslateJobLocal() {
        if (!"local-job".equals(routeConfig.getBatchResultType())) {
            return;
        }
        logger.info("开始执行异步批量文本翻译任务");

        try {
            asyncTextBatchTranslateJobService.processAsyncTextBatchTranslateTasks();
            
            logger.info("异步批量文本翻译任务执行完成");
            
        } catch (Exception e) {
            String errorMsg = "异步批量文本翻译任务执行异常: " + e.getMessage();
            logger.error(errorMsg, e);
        }
    }

    /**
     * 处理异步批量文本翻译回调任务
     */
    //@XxlJob("asyncTextBatchCallbackJobHandler")
    @Scheduled(fixedRate = 1 * 60 * 1000) // 5分钟
    public void asyncTextBatchCallbackJobLocal() {
        if (!"local-job".equals(routeConfig.getBatchResultType())) {
            return;
        }
        logger.info("开始执行异步批量文本翻译回调任务");

        try {
            asyncTextBatchCallbackJobService.processAsyncTextBatchCallbackTasks();
            logger.info("异步批量文本翻译回调任务执行完成");
            
        } catch (Exception e) {
            String errorMsg = "异步批量文本翻译回调任务执行异常: " + e.getMessage();
            logger.error(errorMsg, e);
        }
    }

    /**
     * 清理超时任务
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000)
    public void cleanTimeoutTasksJob() {
        if (!"local-job".equals(routeConfig.getBatchResultType())) {
            return;
        }
        
        try {
            batchTranslateJobService.cleanTimeoutTasksJob();
        } catch (Exception e) {
            logger.error("本地清理超时任务执行异常", e);
        }
    }


}
