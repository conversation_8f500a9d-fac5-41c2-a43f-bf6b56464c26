package com.cashop.translate.service.job;

import com.cashop.translate.dao.entity.TranslateRequestRecord;
import com.cashop.translate.dao.mapper.TranslateRequestRecordMapper;
import com.cashop.translate.facade.dto.callback.TranslateCallbackDTO;
import com.cashop.translate.facade.dto.callback.TranslateImageBatchResult;
import com.cashop.translate.facade.dto.callback.TranslateTextBatchResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 异步批量文本翻译回调定时任务服务
 * 
 * <AUTHOR>
 */
@Service
public class AsyncTextBatchCallbackJobService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncTextBatchCallbackJobService.class);

    @Autowired
    private TranslateRequestRecordMapper translateRequestRecordMapper;

    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    Gson gson = new Gson();

    private final AtomicBoolean isRunning = new AtomicBoolean(false);

    public AsyncTextBatchCallbackJobService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 处理异步批量文本翻译回调任务
     */
    public void processAsyncTextBatchCallbackTasks() {
        if (!isRunning.compareAndSet(false, true)) {
            logger.warn("异步批量文本翻译回调任务正在执行中，跳过本次执行");
            return;
        }
        logger.info("开始处理异步批量文本翻译回调任务");
        
        try {
            // 查询需要回调的异步批量文本翻译任务
            List<TranslateRequestRecord> callbackRecords = translateRequestRecordMapper.selectPendingAsyncTextBatchCallbackTasks();
            
            if (CollectionUtils.isEmpty(callbackRecords)) {
                logger.debug("没有需要回调的异步批量文本翻译任务");
                return;
            }

            logger.info("找到 {} 个需要回调的异步批量文本翻译任务", callbackRecords.size());

            for (TranslateRequestRecord record : callbackRecords) {
                try {
                    processAsyncTextBatchCallbackTask(record);
                } catch (Exception e) {
                    logger.error("处理异步批量文本翻译回调任务失败，recordId: {}, requestId: {}", 
                            record.getId(), record.getRequestId(), e);
                    
                    // 更新回调状态为失败，增加重试次数
                    int retryCount = (record.getCallbackRetryCount() == null ? 0 : record.getCallbackRetryCount()) + 1;
                    translateRequestRecordMapper.updateCallbackStatus(
                            record.getId(), 
                            2, // 回调失败
                            "回调异常: " + e.getMessage(), 
                            retryCount
                    );
                }
            }

        } catch (Exception e) {
            logger.error("处理异步批量文本翻译回调任务异常", e);
        } finally {
            isRunning.set(false);
        }
    }

    /**
     * 处理单个异步批量文本翻译回调任务
     */
    private void processAsyncTextBatchCallbackTask(TranslateRequestRecord record) {
        logger.info("开始处理异步批量文本翻译回调任务，recordId: {}, requestId: {}", 
                record.getId(), record.getRequestId());

        try {
            // 更新回调状态为处理中
            int retryCount = (record.getCallbackRetryCount() == null ? 0 : record.getCallbackRetryCount()) + 1;
            translateRequestRecordMapper.updateCallbackStatus(
                    record.getId(), 
                    3, // 回调处理中
                    null, 
                    retryCount
            );

            // 构建回调数据
            TranslateCallbackDTO callbackData = buildCallbackData(record);
            
            // 执行回调
            String callbackResponse = executeCallback(record.getCallback(), callbackData);

            // 更新回调状态为成功
            translateRequestRecordMapper.updateCallbackStatus(
                    record.getId(), 
                    1, // 回调成功
                    callbackResponse, 
                    retryCount
            );

            logger.info("异步批量文本翻译回调任务处理完成，recordId: {}, requestId: {}", 
                    record.getId(), record.getRequestId());

        } catch (Exception e) {
            logger.error("处理异步批量文本翻译回调任务异常，recordId: {}, requestId: {}", 
                    record.getId(), record.getRequestId(), e);
            throw e;
        }
    }

    /**
     * 构建回调数据
     */
    private TranslateCallbackDTO buildCallbackData(TranslateRequestRecord record) {


        TranslateCallbackDTO callbackData = new TranslateCallbackDTO();
        callbackData.setRequestId(record.getRequestId());
        callbackData.setRequestType(record.getRequestType());
        callbackData.setProvider(record.getProvider());
        callbackData.setSourceLanguage(record.getSourceLanguage());
        callbackData.setTargetLanguage(record.getTargetLanguage());
        callbackData.setRequestStatus(record.getRequestStatus());

        // 翻译结果
        if (StringUtils.hasText(record.getImageSyncResult())) {
            callbackData.setImageSyncResult(record.getImageSyncResult());
        }
        
        if (StringUtils.hasText(record.getImageBatchResults())) {
            callbackData.setImageBatchResults(
                gson.fromJson(record.getImageBatchResults(), 
                    new TypeToken<List<TranslateImageBatchResult>>() {}.getType())
            );
        }
        
        if (StringUtils.hasText(record.getTextSyncResult())) {
            callbackData.setTextSyncResult(record.getTextSyncResult());
        }

        if (StringUtils.hasText(record.getTextBatchResults())) {
            callbackData.setTextBatchResults(
                gson.fromJson(record.getTextBatchResults(), 
                    new TypeToken<List<TranslateTextBatchResult>>() {}.getType())
            );
        }
        
        // 错误信息（如果有）
        if (StringUtils.hasText(record.getErrorMessage())) {
            callbackData.setErrorMessage(record.getErrorMessage());
        }
        
        // 时间信息
        //callbackData.setCreatedTime(record.getCreatedTime());
        //callbackData.setUpdatedTime(record.getUpdatedTime());
        
        return callbackData;
    }

    /**
     * 执行回调
     */
    private String executeCallback(String callbackUrl, TranslateCallbackDTO callbackData) {
        try {
            logger.info("执行回调，URL: {}, 数据: {}", callbackUrl, callbackData);

            // 将回调数据转换为JSON字符串
            String jsonData = gson.toJson(callbackData);

            // 创建请求体
            RequestBody requestBody = RequestBody.create(
                    jsonData,
                    MediaType.parse("application/json; charset=utf-8")
            );

            // 构建请求
            Request request = new Request.Builder()
                    .url(callbackUrl)
                    .post(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("User-Agent", "Translate-Service-Callback/1.0")
                    .build();

            // 发送请求
            try (Response response = httpClient.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";

                if (response.isSuccessful()) {
                    logger.info("回调执行成功，响应状态: {}, 响应内容: {}", response.code(), responseBody);
                    return responseBody;
                } else {
                    logger.error("回调执行失败，响应状态: {}, 响应内容: {}", response.code(), responseBody);
                    throw new RuntimeException("回调执行失败，HTTP状态码: " + response.code() + ", 响应内容: " + responseBody);
                }
            }

        } catch (IOException e) {
            logger.error("执行回调失败，URL: {}", callbackUrl, e);
            throw new RuntimeException("回调执行失败: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("执行回调失败，URL: {}", callbackUrl, e);
            throw new RuntimeException("回调执行失败: " + e.getMessage(), e);
        }
    }
}
