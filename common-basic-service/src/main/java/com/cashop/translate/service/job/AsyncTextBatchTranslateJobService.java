package com.cashop.translate.service.job;

import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.ProviderEnum;
import com.cashop.translate.common.enums.RequestStatusEnum;
import com.cashop.translate.common.enums.RequestTypeEnum;
import com.cashop.translate.dao.entity.TranslateRequestRecord;
import com.cashop.translate.dao.mapper.TranslateRequestRecordMapper;
import com.cashop.translate.service.provider.CloudTranslateProvider;
import com.cashop.translate.service.route.ProviderRouteService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 异步批量文本翻译定时任务服务
 * 
 * <AUTHOR>
 */
@Service
public class AsyncTextBatchTranslateJobService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncTextBatchTranslateJobService.class);

    @Autowired
    private TranslateRequestRecordMapper translateRequestRecordMapper;

    @Autowired
    private ProviderRouteService providerRouteService;

    @Autowired
    private List<CloudTranslateProvider> translateProviders;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Gson gson = new Gson();

    private final AtomicBoolean isRunning = new AtomicBoolean(false);


    /**
     * 处理异步批量文本翻译任务
     */
    public void processAsyncTextBatchTranslateTasks() {
        if (!isRunning.compareAndSet(false, true)) {
            logger.warn("异步批量文本翻译任务正在执行中，跳过本次执行");
            return;
        }

        logger.info("开始处理异步批量文本翻译任务");

        try {
            // 查询待处理的异步批量文本翻译任务
            List<TranslateRequestRecord> pendingRecords = translateRequestRecordMapper.selectPendingAsyncTextBatchTasks();
            
            if (CollectionUtils.isEmpty(pendingRecords)) {
                logger.debug("没有待处理的异步批量文本翻译任务");
                return;
            }

            logger.info("找到 {} 个待处理的异步批量文本翻译任务", pendingRecords.size());

            for (TranslateRequestRecord record : pendingRecords) {
                try {
                    processAsyncTextBatchTranslateTask(record);
                } catch (Exception e) {
                    logger.error("处理异步批量文本翻译任务失败，recordId: {}, requestId: {}", 
                            record.getId(), record.getRequestId(), e);
                    
                    // 更新任务状态为失败
                    translateRequestRecordMapper.updateAsyncTextBatchStatus(
                            record.getId(),
                            RequestStatusEnum.FAILED.getCode(),
                            null,
                            RequestStatusEnum.FAILED.getCode(),
                            record.getRetryCount() + 1,
                            "Job处理异常: " + e.getMessage(),
                            null
                    );
                }
            }

        } catch (Exception e) {
            logger.error("处理异步批量文本翻译任务异常", e);
        } finally {
            isRunning.set(false);
        }
    }

    /**
     * 处理单个异步批量文本翻译任务
     */
    private void processAsyncTextBatchTranslateTask(TranslateRequestRecord record) throws Exception {
        logger.info("开始处理异步批量文本翻译任务，recordId: {}, requestId: {}", 
                record.getId(), record.getRequestId());

        try {
            // 更新任务状态为处理中
            translateRequestRecordMapper.updateAsyncTextBatchStatus(
                    record.getId(),
                    RequestStatusEnum.PROCESSING.getCode(),
                    null,
                    RequestStatusEnum.PROCESSING.getCode(),
                    record.getRetryCount(),
                    null,
                    null
            );

            // 构建翻译请求
            TranslateRequest translateRequest = buildTranslateRequest(record);
            
            // 获取提供商
            CloudTranslateProvider provider = getProviderByCode(record.getProvider());
            if (provider == null) {
                throw new RuntimeException("找不到提供商: " + record.getProvider());
            }

            // 执行翻译
            CompletableFuture<TranslateResponse> future = provider.translateTextBatch(translateRequest);
            TranslateResponse response = future.get();

            // 更新任务结果
            updateTaskResult(record, response);

            logger.info("异步批量文本翻译任务处理完成，recordId: {}, requestId: {}, success: {}", 
                    record.getId(), record.getRequestId(), response.isSuccess());

        } catch (Exception e) {
            logger.error("处理异步批量文本翻译任务异常，recordId: {}, requestId: {}", 
                    record.getId(), record.getRequestId(), e);
            throw e;
        }
    }

    /**
     * 构建翻译请求
     */
    private TranslateRequest buildTranslateRequest(TranslateRequestRecord record) {
        try {
            TranslateRequest request = new TranslateRequest();
            request.setRequestId(record.getRequestId());
            request.setSourceLanguage(record.getSourceLanguage());
            request.setTargetLanguage(record.getTargetLanguage());

            // 解析文本列表
            if (StringUtils.hasText(record.getBatchSourceDataList())) {
                List<String> textList = gson.fromJson(record.getBatchSourceDataList(),
                        new TypeToken<List<String>>() {}.getType());
                request.setTextList(textList);
            }

            // 解析扩展参数
            if (StringUtils.hasText(record.getExt())) {
                try {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> extMap = objectMapper.readValue(record.getExt(), java.util.Map.class);
                    request.setFormatType((String) extMap.get("formatType"));
                    request.setScene((String) extMap.get("scene"));
                } catch (Exception e) {
                    logger.warn("解析扩展参数失败，recordId: {}", record.getId(), e);
                }
            }

            return request;
        } catch (Exception e) {
            logger.error("构建翻译请求失败，recordId: {}", record.getId(), e);
            throw new RuntimeException("构建翻译请求失败", e);
        }
    }

    /**
     * 更新任务结果
     */
    private void updateTaskResult(TranslateRequestRecord record, TranslateResponse response) {
        try {
            String cloudApiAsyncStatus = response.isSuccess() ? RequestStatusEnum.SUCCESS.getCode() : RequestStatusEnum.FAILED.getCode();
            String requestStatus = response.isSuccess() ? RequestStatusEnum.SUCCESS.getCode() : RequestStatusEnum.FAILED.getCode();

            translateRequestRecordMapper.updateAsyncTextBatchStatus(
                    record.getId(),
                    cloudApiAsyncStatus,
                    response.getCloudApiResponse(),
                    requestStatus,
                    record.getRetryCount() + 1,
                    response.getErrorMessage(),
                    response.getTranslateTextResult() // textBatchResults
            );

        } catch (Exception e) {
            logger.error("更新任务结果失败，recordId: {}", record.getId(), e);
            throw new RuntimeException("更新任务结果失败", e);
        }
    }

    /**
     * 根据代码获取提供商
     */
    private CloudTranslateProvider getProviderByCode(String providerCode) {
        ProviderEnum providerEnum = ProviderEnum.fromCode(providerCode);
        return translateProviders.stream()
                .filter(provider -> provider.getProviderType() == providerEnum)
                .findFirst()
                .orElse(null);
    }
}
