package com.cashop.translate.facade.dto;

import java.util.List;

import com.cashop.translate.facade.dto.callback.TranslateTextBatchResult;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 批量翻译结果响应DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "批量翻译结果响应")
public class TranslateBatchResultResponse {

    @Schema(description = "请求ID", example = "req_123456")
    private String requestId;

    @Schema(description = "是否成功", example = "true")
    private boolean success;

    @Schema(description = "任务ID", example = "task_789")
    private String taskId;

    @Schema(description = "请求状态", example = "PENDING/PROCESSING/SUCCESS/FAILED")
    private String requestStatus;

    @Schema(description = "云服务API状态", example = "SUCCESS")
    private String cloudApiStatus;

    @Schema(description = "云服务API响应")
    private String cloudApiResponse;

    @Schema(description = "异步查询结果状态", example = "SUCCESS")
    private String cloudApiAsyncStatus;

    @Schema(description = "异步查询结果响应")
    private String cloudApiAsyncResponse;

    @Schema(description = "错误信息（失败时）")
    private String errorMessage;

    @Schema(description = "处理时间（毫秒）", example = "500")
    private Long processingTime;

    @Schema(description = "源语言", example = "en")
    private String sourceLanguage;

    @Schema(description = "目标语言", example = "zh")
    private String targetLanguage;

    @Schema(description = "图片数量", example = "5")
    private Integer imageCount;

    @Schema(description = "翻译场景", example = "general")
    private String scene;

    @Schema(description = "图片同步翻译结果URL")
    private String translateImageSyncResultUrl;

    @Schema(description = "批量翻译结果", example = "list")
    private List<TranslateBatchResult> TranslateBatchResults;
    
    @Schema(description = "翻译后的文本", example = "你好，世界！")
    private String translatedText;

     @Schema(description = "批量文本翻译结果", example = "list")
    private List<TranslateTextBatchResult> translateTextBatchResults;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(String requestStatus) {
        this.requestStatus = requestStatus;
    }

    public String getCloudApiStatus() {
        return cloudApiStatus;
    }

    public void setCloudApiStatus(String cloudApiStatus) {
        this.cloudApiStatus = cloudApiStatus;
    }

    public String getCloudApiResponse() {
        return cloudApiResponse;
    }

    public void setCloudApiResponse(String cloudApiResponse) {
        this.cloudApiResponse = cloudApiResponse;
    }

    public String getCloudApiAsyncStatus() {
        return cloudApiAsyncStatus;
    }

    public void setCloudApiAsyncStatus(String cloudApiAsyncStatus) {
        this.cloudApiAsyncStatus = cloudApiAsyncStatus;
    }

    public String getCloudApiAsyncResponse() {
        return cloudApiAsyncResponse;
    }

    public void setCloudApiAsyncResponse(String cloudApiAsyncResponse) {
        this.cloudApiAsyncResponse = cloudApiAsyncResponse;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public Integer getImageCount() {
        return imageCount;
    }

    public void setImageCount(Integer imageCount) {
        this.imageCount = imageCount;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public List<TranslateBatchResult> getTranslateBatchResults() {
        return TranslateBatchResults;
    }

    public void setTranslateBatchResults(List<TranslateBatchResult> translateBatchResults) {
        TranslateBatchResults = translateBatchResults;
    }

    public List<TranslateTextBatchResult> getTranslateTextBatchResults() {
        return translateTextBatchResults;
    }

    public void setTranslateTextBatchResults(List<TranslateTextBatchResult> translateTextBatchResults) {
        this.translateTextBatchResults = translateTextBatchResults;
    }

    public String getTranslatedText() {
        return translatedText;
    }

    public void setTranslatedText(String translatedText) {
        this.translatedText = translatedText;
    }

    public String getTranslateImageSyncResultUrl() {
        return translateImageSyncResultUrl;
    }

    public void setTranslateImageSyncResultUrl(String translateImageSyncResultUrl) {
        this.translateImageSyncResultUrl = translateImageSyncResultUrl;
    }

    @Override
    public String toString() {
        return "TranslateBatchResultResponse{" +
                "requestId='" + requestId + '\'' +
                ", success=" + success +
                ", taskId='" + taskId + '\'' +
                ", requestStatus='" + requestStatus + '\'' +
                ", cloudApiStatus='" + cloudApiStatus + '\'' +
                ", cloudApiResponse='" + cloudApiResponse + '\'' +
                ", cloudApiAsyncStatus='" + cloudApiAsyncStatus + '\'' +
                ", cloudApiAsyncResponse='" + cloudApiAsyncResponse + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", processingTime=" + processingTime +
                ", sourceLanguage='" + sourceLanguage + '\'' +
                ", targetLanguage='" + targetLanguage + '\'' +
                ", imageCount=" + imageCount +
                ", scene='" + scene + '\'' +
                ", translateImageSyncResultUrl='" + translateImageSyncResultUrl + '\'' +
                ", TranslateBatchResults=" + TranslateBatchResults +
                ", translatedText='" + translatedText + '\'' +
                '}';

    }
    
}
