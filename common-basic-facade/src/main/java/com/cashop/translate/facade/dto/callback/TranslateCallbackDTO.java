package com.cashop.translate.facade.dto.callback;

//import java.util.Date;
import java.util.List;

public class TranslateCallbackDTO {


        /**
     * 请求ID，用于去重和查询
     */
    private String requestId;

    /**
     * SYNC_IMAGE_SINGLE(同步单张), SYNC_TEXT_SINGLE(同步单条文本), ASYNC_IMAGE_BATCH(异步批量图片), ASYNC_TEXT_BATCH(异步批量文本)
     */
    private String requestType;

    /**
     * 云服务提供商：ALIYUN, BAIDU, TENCENT, HUAWEI
     */
    private String provider;

    /**
     * 源语言
     */
    private String sourceLanguage;

    /**
     * 目标语言
     */
    private String targetLanguage;

    /**
     * 请求状态：PENDING(待处理), PROCESSING(处理中), SUCCESS(成功), FAILED(失败)
     */
    private String requestStatus;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 图片同步翻译结果URL
     */
    private String imageSyncResult;

    /**
     * 图片批量翻译结果
     */
    private List<TranslateImageBatchResult> imageBatchResults;

    /**
     * 文本同步翻译结果
     */
    private String textSyncResult;

    /**
     * 文本批量翻译结果
     */
    private List<TranslateTextBatchResult> textBatchResults;

    /**
     * 创建时间
     */
    //private Date createdTime;

    /**
     * 更新时间
     */
    //private Date updatedTime;

    /**
     * 扩展信息
     */
    private String ext;


    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public String getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(String requestStatus) {
        this.requestStatus = requestStatus;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getImageSyncResult() {
        return imageSyncResult;
    }

    public void setImageSyncResult(String imageSyncResult) {
        this.imageSyncResult = imageSyncResult;
    }

    public List<TranslateImageBatchResult> getImageBatchResults() {
        return imageBatchResults;
    }

    public void setImageBatchResults(List<TranslateImageBatchResult> imageBatchResults) {
        this.imageBatchResults = imageBatchResults;
    }

    public String getTextSyncResult() {
        return textSyncResult;
    }

    public void setTextSyncResult(String textSyncResult) {
        this.textSyncResult = textSyncResult;
    }

    public List<TranslateTextBatchResult> getTextBatchResults() {
        return textBatchResults;
    }

    public void setTextBatchResults(List<TranslateTextBatchResult> textBatchResults) {
        this.textBatchResults = textBatchResults;
    }
    /*
    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }
    */

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    
}
