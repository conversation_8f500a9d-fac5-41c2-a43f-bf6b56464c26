package com.cashop.translate.facade;

import com.cashop.translate.facade.dto.*;
import com.cashop.common.base.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;


/**
 * 翻译服务Facade接口
 * 
 * <AUTHOR>
 */
@FeignClient(name = "common-basic", path = "/api/translate", fallbackFactory = com.cashop.translate.facade.TranslateFacade.TranslateFacadeFallbackFactory.class)
public interface TranslateFacade {

    /**
     * 同步单张图片翻译
     * 
     * @param request 图片同步翻译请求
     * @return 图片同步翻译响应
     */
    @Operation(summary = "同步单张图片翻译", description = "提供单张图片的同步翻译服务")
    @PostMapping("/image/sync")
    Result<TranslateImageSyncResponse> translateImageSync(
            @Valid @RequestBody TranslateImageSyncRequest request);

    /**
     * 异步批量图片翻译
     * 
     * @param request 图片批量翻译请求
     * @return 图片批量翻译响应
     */
    @Operation(summary = "异步批量图片翻译", description = "提供多张图片的异步批量翻译服务")
    @PostMapping("/image/batch")
    Result<TranslateImageBatchResponse> translateImageBatch(
            @Valid @RequestBody TranslateImageBatchRequest request);

    /**
     * 获取批量翻译结果
     * 
     * @param requestId 请求ID
     * @return 批量翻译结果响应
     */
    @Operation(summary = "获取批量翻译结果", description = "根据请求ID获取批量翻译的结果")
    @GetMapping("/image/batch/result")
    Result<TranslateBatchResultResponse> getBatchTranslateResult(
            @Parameter(description = "请求ID", required = true)
            @NotBlank(message = "请求ID不能为空")
            @RequestParam String requestId);

    /**
     * 同步文本翻译
     * 
     * @param request 文本翻译请求
     * @return 文本翻译响应
     */
    @Operation(summary = "同步文本翻译", description = "提供文本的同步翻译服务")
    @PostMapping("/text/sync")
    Result<TranslateTextResponse> translateTextSync(
            @Valid @RequestBody TranslateTextRequest request);

    /**
     * 异步批量文本翻译
     *
     * @param request 批量文本翻译请求
     * @return 批量文本翻译响应
     */
    @Operation(summary = "异步批量文本翻译", description = "提供批量文本的异步翻译服务")
    @PostMapping("/text/batch")
    Result<TranslateTextBatchResponse> translateTextBatch(
            @Valid @RequestBody TranslateTextBatchRequest request);

    /**
     * 异步文本翻译多目标语言
     *
     * @param request 文本翻译请求
     * @return 文本翻译响应
     */
    @Operation(summary = "异步文本翻译多目标语言", description = "提供文本的异步多目标语言翻译服务")
    @PostMapping("/text/async/multi/language")
    Result<TranslateTextMultiLanguageResponse> translateTextMultiLanguageAsync(
            @Valid @RequestBody TranslateTextMultiLanguageRequest request);

    @Component
    public class TranslateFacadeFallbackFactory implements FallbackFactory<TranslateFacade> {
        
        private static final Logger logger = LoggerFactory.getLogger(TranslateFacadeFallbackFactory.class);

        @Override
        public TranslateFacade create(Throwable cause) {
                return new TranslateFacade() {
                        @Override
                        public Result<TranslateImageSyncResponse> translateImageSync(TranslateImageSyncRequest request) {
                                logger.error("调用translate-service的translateImageSync方法失败", cause);
                                return Result.error(cause.getMessage());
                        }

                        @Override
                        public Result<TranslateImageBatchResponse> translateImageBatch(
                                        @Valid TranslateImageBatchRequest request) {
                                logger.error("调用translate-service的translateImageBatch方法失败", cause);
                                return Result.error(cause.getMessage());
                        }

                        @Override
                        public Result<TranslateBatchResultResponse> getBatchTranslateResult(
                                        @NotBlank(message = "请求ID不能为空") String requestId) {
                                logger.error("调用translate-service的getBatchTranslateResult方法失败", cause);
                                return Result.error(cause.getMessage());
                        }

                        @Override
                        public Result<TranslateTextResponse> translateTextSync(@Valid TranslateTextRequest request) {
                                logger.error("调用translate-service的translateTextSync方法失败", cause);
                                return Result.error(cause.getMessage());
                        }

                        @Override
                        public Result<TranslateTextBatchResponse> translateTextBatch(@Valid TranslateTextBatchRequest request) {
                                logger.error("调用translate-service的translateTextBatch方法失败", cause);
                                return Result.error(cause.getMessage());
                        }

                        @Override
                        public Result<TranslateTextMultiLanguageResponse> translateTextMultiLanguageAsync(
                                        @Valid TranslateTextMultiLanguageRequest request) {
                                logger.error("调用translate-service的translateTextMultiLanguageAsync方法失败", cause);
                                return Result.error(cause.getMessage());
                        }
                        
                };
        }


    }
}
